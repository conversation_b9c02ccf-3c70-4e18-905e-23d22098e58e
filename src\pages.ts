import typescriptLogo from "./typescript.svg";
import viteLogo from "/vite.svg";
import { setupCounter } from "./counter.ts";

// 页面路由管理
export function navigateToPage(pageName: string) {
  const app = document.querySelector<HTMLDivElement>("#app")!;

  switch (pageName) {
    case "home":
      app.innerHTML = getHomePage();
      break;
    case "login":
      app.innerHTML = getLoginPage();
      break;
    case "about":
      app.innerHTML = getAboutPage();
      break;
    default:
      app.innerHTML = getHomePage();
  }

  // 重新绑定事件
  bindEvents();
}

function getHomePage() {
  return `
    <div class="min-h-screen bg-gradient-to-br from-slate-900 to-slate-700 flex items-center justify-center p-4">
      <div class="max-w-4xl mx-auto text-center">
        <!-- Logo区域 -->
        <div class="flex justify-center items-center gap-8 mb-8">
          <a href="https://vite.dev" target="_blank" class="group">
            <img src="${viteLogo}" class="logo h-24 p-6 transition-all duration-300 group-hover:scale-110" alt="Vite logo" />
          </a>
          <a href="https://www.typescriptlang.org/" target="_blank" class="group">
            <img src="${typescriptLogo}" class="logo vanilla h-24 p-6 transition-all duration-300 group-hover:scale-110" alt="TypeScript logo" />
          </a>
        </div>

        <!-- 标题 -->
        <h1 class="text-5xl font-bold text-white mb-4 bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">
          Tailwind CSS 4.1 练习
        </h1>
        <p class="text-xl text-slate-300 mb-8">使用 Vite + TypeScript + Tailwind CSS 4.1</p>

        <!-- 卡片区域 -->
        <div class="bg-white/10 backdrop-blur-lg rounded-2xl p-8 mb-8 border border-white/20">
          <h2 class="text-2xl font-semibold text-white mb-4">计数器示例</h2>
          <button id="counter" type="button"
                  class="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700
                         text-white font-semibold py-3 px-8 rounded-lg transition-all duration-300
                         transform hover:scale-105 hover:shadow-lg focus:outline-none focus:ring-4 focus:ring-blue-300/50">
          </button>
        </div>

        <!-- 页面导航 -->
        <div class="flex gap-4 justify-center mb-8">
          <button onclick="navigateToPage('login')"
                  class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition-colors">
            登录页面
          </button>
          <button onclick="navigateToPage('about')"
                  class="bg-purple-500 hover:bg-purple-600 text-white px-6 py-3 rounded-lg transition-colors">
            关于页面
          </button>
        </div>

        <!-- 功能展示区域 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div class="bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10">
            <div class="text-3xl mb-3">🎨</div>
            <h3 class="text-lg font-semibold text-white mb-2">现代设计</h3>
            <p class="text-slate-300 text-sm">使用 Tailwind CSS 4.1 的最新功能</p>
          </div>
          <div class="bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10">
            <div class="text-3xl mb-3">⚡</div>
            <h3 class="text-lg font-semibold text-white mb-2">快速开发</h3>
            <p class="text-slate-300 text-sm">Vite 提供极速的开发体验</p>
          </div>
          <div class="bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10">
            <div class="text-3xl mb-3">🔧</div>
            <h3 class="text-lg font-semibold text-white mb-2">类型安全</h3>
            <p class="text-slate-300 text-sm">TypeScript 提供完整的类型支持</p>
          </div>
        </div>

        <p class="text-slate-400 text-sm">
          点击上方的 Vite 和 TypeScript 图标了解更多
        </p>
      </div>
    </div>
  `;
}

function getLoginPage() {
  return `
    <div class="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center p-4">
      <div class="w-full max-w-md">
        <div class="bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/20 shadow-2xl">
          <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-white mb-2">登录页面</h1>
            <p class="text-slate-300">这是通过 TypeScript 渲染的页面</p>
          </div>
          
          <form class="space-y-6">
            <div>
              <label class="block text-sm font-medium text-slate-200 mb-2">邮箱地址</label>
              <input type="email" 
                     class="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                     placeholder="请输入您的邮箱">
            </div>
            
            <div>
              <label class="block text-sm font-medium text-slate-200 mb-2">密码</label>
              <input type="password" 
                     class="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                     placeholder="请输入您的密码">
            </div>
            
            <button type="submit"
                    class="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-300">
              登录
            </button>
          </form>
          
          <div class="mt-6 text-center">
            <button onclick="navigateToPage('home')" 
                    class="text-blue-400 hover:text-blue-300 transition-colors">
              ← 返回首页
            </button>
          </div>
        </div>
      </div>
    </div>
  `;
}

function getAboutPage() {
  return `
    <div class="min-h-screen bg-gradient-to-br from-green-900 via-teal-900 to-blue-900 flex items-center justify-center p-4">
      <div class="max-w-2xl mx-auto text-center">
        <div class="bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/20">
          <h1 class="text-4xl font-bold text-white mb-6">关于页面</h1>
          <p class="text-slate-300 mb-6 leading-relaxed">
            这是一个使用 Tailwind CSS 4.1 构建的单页应用示例。
            展示了如何在 TypeScript 中管理多个页面。
          </p>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div class="bg-white/5 p-4 rounded-lg">
              <h3 class="text-lg font-semibold text-white mb-2">🎨 现代设计</h3>
              <p class="text-slate-300 text-sm">使用最新的 Tailwind CSS 特性</p>
            </div>
            <div class="bg-white/5 p-4 rounded-lg">
              <h3 class="text-lg font-semibold text-white mb-2">⚡ 快速开发</h3>
              <p class="text-slate-300 text-sm">Vite 提供极速的开发体验</p>
            </div>
          </div>
          
          <button onclick="navigateToPage('home')" 
                  class="bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg transition-colors">
            返回首页
          </button>
        </div>
      </div>
    </div>
  `;
}

function bindEvents() {
  // 将 navigateToPage 函数绑定到全局作用域
  (window as any).navigateToPage = navigateToPage;

  // 设置计数器（如果存在）
  const counterButton = document.querySelector<HTMLButtonElement>("#counter");
  if (counterButton) {
    setupCounter(counterButton);
  }
}
