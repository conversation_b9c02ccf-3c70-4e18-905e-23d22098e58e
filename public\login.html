<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录页面 - Tailwind CSS 4.1</title>
    <link rel="stylesheet" href="/src/style.css">
</head>
<body>
    <div class="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center p-4">
        <div class="w-full max-w-md">
            <!-- 登录卡片 -->
            <div class="bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/20 shadow-2xl">
                <!-- 标题 -->
                <div class="text-center mb-8">
                    <h1 class="text-3xl font-bold text-white mb-2">欢迎回来</h1>
                    <p class="text-slate-300">请登录您的账户</p>
                </div>

                <!-- 登录表单 -->
                <form class="space-y-6">
                    <!-- 邮箱输入 -->
                    <div>
                        <label for="email" class="block text-sm font-medium text-slate-200 mb-2">
                            邮箱地址
                        </label>
                        <input 
                            type="email" 
                            id="email" 
                            name="email"
                            class="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-lg 
                                   text-white placeholder-slate-400 focus:outline-none focus:ring-2 
                                   focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                            placeholder="请输入您的邮箱"
                            required
                        >
                    </div>

                    <!-- 密码输入 -->
                    <div>
                        <label for="password" class="block text-sm font-medium text-slate-200 mb-2">
                            密码
                        </label>
                        <input 
                            type="password" 
                            id="password" 
                            name="password"
                            class="w-full px-4 py-3 bg-white/5 border border-white/20 rounded-lg 
                                   text-white placeholder-slate-400 focus:outline-none focus:ring-2 
                                   focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                            placeholder="请输入您的密码"
                            required
                        >
                    </div>

                    <!-- 记住我 -->
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <input 
                                type="checkbox" 
                                id="remember" 
                                name="remember"
                                class="w-4 h-4 text-blue-600 bg-white/10 border-white/20 rounded 
                                       focus:ring-blue-500 focus:ring-2"
                            >
                            <label for="remember" class="ml-2 text-sm text-slate-300">
                                记住我
                            </label>
                        </div>
                        <a href="#" class="text-sm text-blue-400 hover:text-blue-300 transition-colors">
                            忘记密码？
                        </a>
                    </div>

                    <!-- 登录按钮 -->
                    <button 
                        type="submit"
                        class="w-full bg-gradient-to-r from-blue-500 to-purple-600 
                               hover:from-blue-600 hover:to-purple-700 text-white font-semibold 
                               py-3 px-4 rounded-lg transition-all duration-300 transform 
                               hover:scale-105 focus:outline-none focus:ring-4 focus:ring-blue-300/50"
                    >
                        登录
                    </button>
                </form>

                <!-- 分割线 -->
                <div class="my-6 flex items-center">
                    <div class="flex-1 border-t border-white/20"></div>
                    <span class="px-4 text-sm text-slate-400">或</span>
                    <div class="flex-1 border-t border-white/20"></div>
                </div>

                <!-- 社交登录 -->
                <div class="space-y-3">
                    <button class="w-full bg-white/5 hover:bg-white/10 border border-white/20 
                                   text-white py-3 px-4 rounded-lg transition-all duration-300 
                                   flex items-center justify-center space-x-2">
                        <span>🔗</span>
                        <span>使用 GitHub 登录</span>
                    </button>
                    <button class="w-full bg-white/5 hover:bg-white/10 border border-white/20 
                                   text-white py-3 px-4 rounded-lg transition-all duration-300 
                                   flex items-center justify-center space-x-2">
                        <span>📧</span>
                        <span>使用 Google 登录</span>
                    </button>
                </div>

                <!-- 注册链接 -->
                <div class="mt-6 text-center">
                    <p class="text-slate-400">
                        还没有账户？ 
                        <a href="#" class="text-blue-400 hover:text-blue-300 transition-colors font-medium">
                            立即注册
                        </a>
                    </p>
                </div>
            </div>

            <!-- 返回首页 -->
            <div class="mt-6 text-center">
                <a href="/" class="text-slate-400 hover:text-white transition-colors text-sm">
                    ← 返回首页
                </a>
            </div>
        </div>
    </div>
</body>
</html>
