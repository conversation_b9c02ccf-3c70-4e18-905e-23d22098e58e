# Tailwind CSS 4.1 练习项目

这是一个使用 Tailwind CSS 4.1 + Vite + TypeScript 的练习项目。

## 🚀 快速开始

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

## 📁 项目结构

```
tailwind_test/
├── src/
│   ├── main.ts          # 主入口文件
│   ├── style.css        # Tailwind CSS 样式
│   ├── counter.ts       # 计数器功能
│   └── vite-env.d.ts    # TypeScript 类型定义
├── public/              # 静态资源
├── index.html           # HTML 模板
├── vite.config.ts       # Vite 配置
├── tsconfig.json        # TypeScript 配置
└── package.json         # 项目依赖
```

## 🎨 Tailwind CSS 4.1 特性

本项目展示了 Tailwind CSS 4.1 的以下特性：

- **新的导入语法**: `@import "tailwindcss"`
- **现代渐变**: `bg-gradient-to-br`
- **玻璃态效果**: `backdrop-blur-lg`
- **透明度语法**: `bg-white/10`
- **响应式设计**: `grid-cols-1 md:grid-cols-3`
- **动画效果**: `transition-all duration-300`
- **悬停效果**: `hover:scale-105`

## 🛠️ 技术栈

- **Vite**: 快速的前端构建工具
- **TypeScript**: 类型安全的 JavaScript
- **Tailwind CSS 4.1**: 实用优先的 CSS 框架
- **@tailwindcss/vite**: Tailwind CSS 的 Vite 插件

## 📚 学习资源

- [Tailwind CSS 4.1 文档](https://tailwindcss.com/docs)
- [Vite 文档](https://vitejs.dev/)
- [TypeScript 文档](https://www.typescriptlang.org/)

## 📄 创建新页面的方法

### 方法1: 在 public 目录创建 HTML 文件（推荐）
```bash
# 创建 public/login.html
# 访问: http://localhost:5173/login.html
```

### 方法2: 使用 TypeScript 页面路由系统
```typescript
// 在 src/pages.ts 中添加新页面
// 通过 navigateToPage('pageName') 切换页面
```

### 方法3: 配置 Vite 多页面应用
```typescript
// 在 vite.config.ts 中配置多个入口点
// 支持构建时的多页面优化
```

## 🎯 练习建议

1. 尝试修改颜色主题
2. 添加新的组件
3. 实验不同的布局
4. 练习响应式设计
5. 探索动画效果
6. 创建自己的页面
